package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionApiResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionBodyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.BodyFormatterUtils;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionApiRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionApiService;

@Service
@RequiredArgsConstructor
public class ExecutionApiServiceImpl extends BaseServiceImpl<ExecutionApiEntity, String>
        implements ExecutionApiService {

  private final ExecutionApiRepository executionApiRepository;
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_WITH_SSL)
  private final RestTemplate restTemplateWithSsl;
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_SKIP_SSL)
  private final RestTemplate restTemplateSkipSsl;

  private final Logger logger = LoggerFactory.getLogger(this.getClass());

  @Override
  protected JpaCommonRepository<ExecutionApiEntity, String> getRepository() {
    return executionApiRepository;
  }

  @Override
  public ExecutionApiEntity findAllByExecutionId(String executionId) {
    return executionApiRepository.findAllByExecutionId(executionId);
  }

  @Override
  public ExecutionApiResponse callExecutionApi(ExecutionApiEntity executionApiInfo) {
    long start = System.currentTimeMillis();
    try {
      RestTemplate restTemplate = Boolean.TRUE.equals(executionApiInfo.getEnableSsl())
              ? restTemplateWithSsl
              : restTemplateSkipSsl;

      HttpHeaders headers = new HttpHeaders();
      if (!KanbanCommonUtil.isEmpty(executionApiInfo.getHeaders())) {
        for (ExecutionKeyValue kv : executionApiInfo.getHeaders()) {
          if (kv.isEnable()) {
            headers.add(kv.getKey(), kv.getValue());
          }
        }
      }

      // Auth headers
      switch (executionApiInfo.getAuthType()) {
        case TOKEN -> headers.setBearerAuth(executionApiInfo.getAuthToken());
        case BASIC -> {
          if (!KanbanCommonUtil.isEmpty(executionApiInfo.getUsername())
                  && !KanbanCommonUtil.isEmpty(executionApiInfo.getPassword())) {
            headers.setBasicAuth(executionApiInfo.getUsername(), executionApiInfo.getPassword());
          }
        }
        default -> {
          // do nothing
        }
      }

      Object requestBody = null;
      if (ExecutionBodyTypeEnum.RAW.equals(executionApiInfo.getBodyType())) {
        requestBody = executionApiInfo.getBodyRaw();
      } else if (ExecutionBodyTypeEnum.URLENCODED.equals(executionApiInfo.getBodyType())) {
        MultiValueMap<String, String> formMap = new LinkedMultiValueMap<>();
        if (!KanbanCommonUtil.isEmpty(executionApiInfo.getFormUrlEncoded())) {
          for (ExecutionKeyValue kv : executionApiInfo.getFormUrlEncoded()) {
            if (kv.isEnable()) {
              formMap.add(kv.getKey(), kv.getValue());
            }
          }
        }
        requestBody = formMap;
      }

      HttpEntity<?> httpEntity = new HttpEntity<>(requestBody, headers);

      // Build URL with query params
      String url = executionApiInfo.getUrl();
      var params = executionApiInfo.getParams();
      if (!KanbanCommonUtil.isEmpty(params) && !params.isEmpty()) {
        StringBuilder sb = new StringBuilder(url.contains("?") ? url : url + "?");
        for (ExecutionKeyValue kv : params) {
          if (kv.isEnable()) {
            sb.append(kv.getKey()).append("=").append(kv.getValue()).append("&");
          }
        }
        url = sb.substring(0, sb.length() - 1); // remove trailing &
      }

      // Send request
      ResponseEntity<String> response = restTemplate.exchange(
              url,
              HttpMethod.valueOf(executionApiInfo.getMethod().name()),
              httpEntity,
              String.class
      );

      long duration = System.currentTimeMillis() - start;
      HttpStatus statusEnum = HttpStatus.resolve(response.getStatusCode().value());
      String formatted = null;
      if (!KanbanCommonUtil.isEmpty(response.getBody())
              && !KanbanCommonUtil.isEmpty(response.getHeaders().getContentType())) {
        formatted = BodyFormatterUtils.format(response.getBody(),
                response.getHeaders().getContentType().toString());
      }
      return ExecutionApiResponse.builder()
              .status(statusEnum)
              .statusCode(!KanbanCommonUtil.isEmpty(statusEnum) ? statusEnum.value() : 500)
              .statusText(!KanbanCommonUtil.isEmpty(statusEnum) ? statusEnum.getReasonPhrase() : "")
              .contentType(!KanbanCommonUtil.isEmpty(response.getHeaders().getContentType())
                      ? response.getHeaders().getContentType().toString()
                      : null)
              .durationMillis(duration)
              .body(KanbanCommonUtil.isEmpty(formatted) ? response.getBody() : formatted)
              .build();
    } catch (HttpClientErrorException ex) {
      logger.info("Failure call api {}", ex.getMessage());
      var responseHeader = ex.getResponseHeaders();
      boolean hasContentType = !KanbanCommonUtil.isEmpty(responseHeader)
              && !KanbanCommonUtil.isEmpty(responseHeader.getContentType());
      String formatted = ex.getResponseBodyAsString();
      if (hasContentType && !KanbanCommonUtil.isEmpty(ex.getResponseBodyAsString())) {
        formatted = BodyFormatterUtils.format(formatted,
                responseHeader.getContentType().toString());
      }
      return ExecutionApiResponse.builder()
              .statusCode(ex.getStatusCode().value())
              .status((HttpStatus) ex.getStatusCode())
              .statusText(((HttpStatus) ex.getStatusCode()).getReasonPhrase())
              .contentType(hasContentType
                      ? responseHeader.getContentType().toString() : null)
              .durationMillis(System.currentTimeMillis() - start)
              .body(formatted)
              .build();
    } catch (Exception e) {
      logger.error("Could not send request to url {}: {}", executionApiInfo.getUrl(), e.getMessage());
      return ExecutionApiResponse.builder()
              .contentType("text/plain")
              .durationMillis(System.currentTimeMillis() - start)
              .body("Could not send request: " + e.getMessage())
              .status(HttpStatus.INTERNAL_SERVER_ERROR)
              .statusCode(500)
              .build();
    }
  }
}
