package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.VariableRepository;

@ExtendWith(MockitoExtension.class)
class VariableServiceImplTest {

  @Mock
  private VariableRepository variableRepository;

  @InjectMocks
  private VariableServiceImpl variableService;

  @Test
  void getRepository_success_returnsConfiguredRepository() {
    // Act
    JpaCommonRepository<VariableEntity, String> repository = variableService.getRepository();

    // Assert
    assertNotNull(repository);
    assertSame(variableRepository, repository, "The returned repository should be the mocked instance.");
  }

  @Test
  void findWithId_success_returnsVariableEntity() throws BusinessException {
    // Arrange
    String variableId = "test-variable-id";
    VariableEntity expectedVariable = createTestVariableEntity();
    expectedVariable.setId(variableId);
    
    when(variableRepository.findById(variableId)).thenReturn(Optional.of(expectedVariable));

    // Act
    VariableEntity result = variableService.findWithId(variableId);

    // Assert
    assertNotNull(result);
    assertEquals(expectedVariable, result);
    assertEquals(variableId, result.getId());
    assertEquals("Test Variable", result.getName());
    assertEquals("Test Description", result.getDescription());
    assertEquals("test-value", result.getValue());
  }

  @Test
  void findWithId_notFound_throwsBusinessException() {
    // Arrange
    String variableId = "non-existent-id";
    when(variableRepository.findById(variableId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      variableService.findWithId(variableId);
    });

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findWithId_nullId_throwsBusinessException() {
    // Arrange
    String variableId = null;
    when(variableRepository.findById(variableId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      variableService.findWithId(variableId);
    });

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findWithId_emptyId_throwsBusinessException() {
    // Arrange
    String variableId = "";
    when(variableRepository.findById(variableId)).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      variableService.findWithId(variableId);
    });

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void findWithId_hiddenVariable_returnsVariableEntity() throws BusinessException {
    // Arrange
    String variableId = "hidden-variable-id";
    VariableEntity hiddenVariable = createTestVariableEntity();
    hiddenVariable.setId(variableId);
    hiddenVariable.setHidden(true);
    hiddenVariable.setValue("secret-value");
    
    when(variableRepository.findById(variableId)).thenReturn(Optional.of(hiddenVariable));

    // Act
    VariableEntity result = variableService.findWithId(variableId);

    // Assert
    assertNotNull(result);
    assertEquals(hiddenVariable, result);
    assertEquals(variableId, result.getId());
    assertEquals(true, result.isHidden());
    assertEquals("secret-value", result.getValue());
  }

  /**
   * Creates a test VariableEntity for use in tests.
   */
  private VariableEntity createTestVariableEntity() {
    VariableEntity entity = new VariableEntity();
    entity.setId("test-variable-id");
    entity.setName("Test Variable");
    entity.setDescription("Test Description");
    entity.setValue("test-value");
    entity.setHidden(false);
    return entity;
  }
}
