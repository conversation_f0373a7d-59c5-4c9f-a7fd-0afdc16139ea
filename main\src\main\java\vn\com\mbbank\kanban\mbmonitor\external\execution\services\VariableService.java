package vn.com.mbbank.kanban.mbmonitor.external.execution.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;


/**
 * interface logic Variable.
 */
public interface VariableService extends BaseService<VariableEntity, String> {

  /**
   * Find all Variable.
   *
   * @param id id
   * @return page VariableEntity
   */
  VariableEntity findWithId(String id) throws BusinessException;

}