package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.zaxxer.hikari.HikariDataSource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.mapper.ExecutionHistoryEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionParamService;

/**
 * Unit tests for ExecutionServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class ExecutionServiceImplTest {

  @Mock
  private ExecutionHistoryService executionHistoryService;

  @Mock
  private ExecutionRepository executionRepository;

  @Mock
  private DatabaseConnectionService databaseConnectionService;

  @Mock
  private QueryHikariDataSourceConfig queryHikariDataSourceConfig;

  @Mock
  private DatabaseQueryService databaseQueryService;

  @InjectMocks
  private ExecutionServiceImpl executionService;

  @Mock
  private ExecutionParamService executionParamService;

  @Test
  void execute_success_withValidRequest() throws BusinessException {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    ExecutionScriptResponse expectedResponse = new ExecutionScriptResponse();
    expectedResponse.setStatus(ExecutionStatusEnum.COMPLETED);

    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.of(execution));

    ExecutionServiceImpl spyService = spy(executionService);
    doReturn(expectedResponse).when(spyService).runExecution(request);

    // Act
    ExecutionScriptResponse response = spyService.execute(request);

    // Assert
    assertEquals(expectedResponse, response);
    verify(spyService).runExecution(request);
  }

  // ========== VALIDATION TESTS ==========

  @Test
  void validateRequest_success_withValidSqlRequest() throws BusinessException {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL);
    execution.setDatabaseConnectionId(1L);

    DatabaseConnectionEntity dbConnection = createTestDatabaseConnection();
    dbConnection.setIsActive(true);

    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.of(execution));
    when(databaseConnectionService.findById(1L)).thenReturn(dbConnection);

    // Act & Assert - should not throw exception
    executionService.validateRequest(request);
  }

  @Test
  void validateRequest_failed_executionNotFound() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.empty());

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.validateRequest(request);
    });
    assertEquals(ErrorCode.EXECUTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void validateRequest_failed_databaseConnectionNotFound() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL);
    execution.setDatabaseConnectionId(1L);

    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.of(execution));
    when(databaseConnectionService.findById(1L)).thenReturn(null);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.validateRequest(request);
    });
    assertEquals(ErrorCode.DATABASE_CONNECTION_NOT_FOUND.getCode(), exception.getCode());
  }

  @Test
  void validateRequest_failed_databaseConnectionInactive() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL);
    execution.setDatabaseConnectionId(1L);

    DatabaseConnectionEntity dbConnection = createTestDatabaseConnection();
    dbConnection.setIsActive(false);

    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.of(execution));
    when(databaseConnectionService.findById(1L)).thenReturn(dbConnection);

    // Act & Assert
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      executionService.validateRequest(request);
    });
    assertEquals(ErrorCode.DATABASE_COLLECT_CONNECTION_INACTIVE.getCode(), exception.getCode());
  }

  @Test
  void validateRequest_success_withPythonRequest() throws BusinessException {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.PYTHON);

    when(executionRepository.findById(request.getExecutionId())).thenReturn(Optional.of(execution));

    // Act & Assert - should not throw exception
    executionService.validateRequest(request);
  }

  // ========== EXECUTION TYPE TESTS ==========

  @Test
  void runExecution_success_pythonType() throws BusinessException {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.PYTHON);

    ExecutionHistoryEntity historyEntity = new ExecutionHistoryEntity();
    ExecutionScriptResponse expectedResponse = new ExecutionScriptResponse();
    expectedResponse.setStatus(ExecutionStatusEnum.COMPLETED);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);
    when(executionHistoryService.save(any(ExecutionHistoryEntity.class))).thenReturn(historyEntity);

    ExecutionServiceImpl spyService = spy(executionService);
    doReturn(expectedResponse).when(spyService).runPythonScript(request, new ArrayList<>());
    doNothing().when(spyService).saveCompleteExecutionHistory(any(), any(), any());

    // Act
    ExecutionScriptResponse response = spyService.runExecution(request);

    // Assert
    assertEquals(expectedResponse, response);
    verify(spyService).runPythonScript(request, new ArrayList<>());
    verify(executionHistoryService).save(any(ExecutionHistoryEntity.class));
  }

  @Test
  void runExecution_success_sqlType() throws BusinessException {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL);

    ExecutionScriptResponse expectedResponse = new ExecutionScriptResponse();
    expectedResponse.setStatus(ExecutionStatusEnum.COMPLETED);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);

    ExecutionServiceImpl spyService = spy(executionService);
    doReturn(expectedResponse).when(spyService).runSqlScript(request, new ArrayList<>());
    doNothing().when(spyService).saveCompleteExecutionHistory(any(), any(), any());

    // Act
    ExecutionScriptResponse response = spyService.runExecution(request);

    // Assert
    assertEquals(expectedResponse, response);
    verify(spyService).runSqlScript(request, new ArrayList<>());
    verify(executionHistoryService, never()).save(any(ExecutionHistoryEntity.class));
  }

  // ========== HISTORY STORAGE TESTS ==========

  @Test
  void canStoreHistory_true_forPythonType() {
    // Arrange
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.PYTHON);

    // Act
    boolean result = executionService.canStoreHistory(execution);

    // Assert
    assertTrue(result);
  }

  @Test
  void canStoreHistory_false_forSqlType() {
    // Arrange
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL);

    // Act
    boolean result = executionService.canStoreHistory(execution);

    // Assert
    assertFalse(result);
  }

  @Test
  void canStoreHistory_false_forApiType() {
    // Arrange
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.API);

    // Act
    boolean result = executionService.canStoreHistory(execution);

    // Assert
    assertTrue(result);
  }

  @Test
  void saveCompleteExecutionHistory_success_withValidHistory() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionScriptResponse response = new ExecutionScriptResponse();
    response.setStatus(ExecutionStatusEnum.COMPLETED);
    response.setResult("Test result");
    response.setError("Test error");

    ExecutionHistoryEntity historyEntity = new ExecutionHistoryEntity();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.PYTHON);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);
    when(executionHistoryService.save(historyEntity)).thenReturn(historyEntity);

    // Act
    executionService.saveCompleteExecutionHistory(historyEntity, request, response);

    // Assert
    verify(executionHistoryService).save(historyEntity);
    assertEquals(response.getResult(), historyEntity.getResult());
    assertEquals(response.getStatus(), historyEntity.getStatus());
    assertEquals(response.getError(), historyEntity.getError());
    assertEquals(request.getExecutionBy(), historyEntity.getExecutionBy());
    assertNotNull(historyEntity.getEndTime());
  }

  @Test
  void saveCompleteExecutionHistory_noAction_withNullHistory() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionScriptResponse response = new ExecutionScriptResponse();

    // Act
    executionService.saveCompleteExecutionHistory(null, request, response);

    // Assert
    verify(executionHistoryService, never()).save(any(ExecutionHistoryEntity.class));
  }

  @Test
  void saveCompleteExecutionHistory_noAction_forNonPythonType() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionScriptResponse response = new ExecutionScriptResponse();
    ExecutionHistoryEntity historyEntity = new ExecutionHistoryEntity();
    ExecutionEntity execution = createTestExecution();
    execution.setType(ExecutionTypeEnum.SQL); // Non-Python type

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);

    // Act
    executionService.saveCompleteExecutionHistory(historyEntity, request, response);

    // Assert
    verify(executionHistoryService, never()).save(historyEntity);
  }

  // ========== SQL SCRIPT EXECUTION TESTS ==========

  @Test
  void runSqlScript_success_selectQuery() throws Exception {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    request.setPaginationRequest(new PaginationRequestDTO());

    ExecutionEntity execution = createTestExecution();
    execution.setScript("SELECT * FROM users");
    execution.setDatabaseConnectionId(1L);

    DatabaseConnectionEntity dbConnection = createTestDatabaseConnection();
    HikariDataSource dataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);

    SqlExecutionResponse sqlResponse = new SqlExecutionResponse();
    sqlResponse.setNonQuery(false);
    sqlResponse.setListDataMappings(Collections.emptyList());

    SqlExecutionResponse totalResponse = new SqlExecutionResponse();
    totalResponse.setListDataMappings(List.of(
            SqlExecutionResponse.SqlDataMapping.builder()
                    .listSqlMappingColumnDatas(List.of(
                            SqlExecutionResponse.SqlMappingColumnData.builder()
                                    .column("count")
                                    .value("10")
                                    .build()
                    ))
                    .build()
    ));

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);
    when(databaseConnectionService.findById(1L)).thenReturn(dbConnection);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(dataSource);
    when(dataSource.getConnection()).thenReturn(connection);
    when(databaseQueryService.executeQuery(eq(connection), anyString(), eq(true), eq(null), anyMap()))
            .thenReturn(sqlResponse)
            .thenReturn(totalResponse);

    ExecutionServiceImpl spyService = spy(executionService);
    doReturn(true).when(spyService).isSelectQuery(anyString());

    // Act
    ExecutionScriptResponse response = spyService.runSqlScript(request, new ArrayList<>());

    // Assert
    assertEquals(ExecutionStatusEnum.COMPLETED, response.getStatus());
    assertNotNull(response.getSqlExecutionResponse());
    assertEquals(10, response.getSqlExecutionResponse().getTotal());
    verify(databaseQueryService, times(2)).executeQuery(eq(connection), anyString(), eq(true), eq(null), anyMap());
  }

  @Test
  void runSqlScript_success_nonSelectQuery() throws Exception {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();

    ExecutionEntity execution = createTestExecution();
    execution.setScript("UPDATE users SET name = 'test'");
    execution.setDatabaseConnectionId(1L);

    DatabaseConnectionEntity dbConnection = createTestDatabaseConnection();
    HikariDataSource dataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);

    SqlExecutionResponse sqlResponse = new SqlExecutionResponse();
    sqlResponse.setNonQuery(true);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);
    when(databaseConnectionService.findById(1L)).thenReturn(dbConnection);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(dataSource);
    when(dataSource.getConnection()).thenReturn(connection);
    when(databaseQueryService.executeQuery(eq(connection), anyString(), eq(true), eq(null), anyMap()))
            .thenReturn(sqlResponse);

    ExecutionServiceImpl spyService = spy(executionService);
    doReturn(false).when(spyService).isSelectQuery(anyString());

    // Act
    ExecutionScriptResponse response = spyService.runSqlScript(request, new ArrayList<>());

    // Assert
    assertEquals(ExecutionStatusEnum.COMPLETED, response.getStatus());
    assertNotNull(response.getSqlExecutionResponse());
    verify(databaseQueryService, times(1)).executeQuery(eq(connection), anyString(), eq(true), eq(null), anyMap());
  }

  @Test
  void runSqlScript_failed_withException() throws Exception {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();

    ExecutionEntity execution = createTestExecution();
    execution.setScript("INVALID SQL");
    execution.setDatabaseConnectionId(1L);

    DatabaseConnectionEntity dbConnection = createTestDatabaseConnection();
    HikariDataSource dataSource = mock(HikariDataSource.class);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);
    when(databaseConnectionService.findById(1L)).thenReturn(dbConnection);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(dataSource);
    when(dataSource.getConnection()).thenThrow(new SQLException("Connection failed"));

    // Act
    ExecutionScriptResponse response = executionService.runSqlScript(request, new ArrayList<>());

    // Assert
    assertEquals(ExecutionStatusEnum.FAILED, response.getStatus());
    assertNotNull(response.getError());
    assertTrue(response.getError().contains("Connection failed"));
  }

  // ========== UTILITY METHOD TESTS ==========

  @Test
  void isSelectQuery_true_forSelectStatement() {
    // Arrange
    String selectSql = "SELECT * FROM users WHERE id = 1";

    // Act
    boolean result = executionService.isSelectQuery(selectSql);

    // Assert
    assertTrue(result);
  }

  @Test
  void isSelectQuery_false_forUpdateStatement() {
    // Arrange
    String updateSql = "UPDATE users SET name = 'test' WHERE id = 1";

    // Act
    boolean result = executionService.isSelectQuery(updateSql);

    // Assert
    assertFalse(result);
  }

  @Test
  void isSelectQuery_false_forInsertStatement() {
    // Arrange
    String insertSql = "INSERT INTO users (name) VALUES ('test')";

    // Act
    boolean result = executionService.isSelectQuery(insertSql);

    // Assert
    assertFalse(result);
  }

  @Test
  void isSelectQuery_false_forDeleteStatement() {
    // Arrange
    String deleteSql = "DELETE FROM users WHERE id = 1";

    // Act
    boolean result = executionService.isSelectQuery(deleteSql);

    // Assert
    assertFalse(result);
  }

  @Test
  void isSelectQuery_false_forInvalidSql() {
    // Arrange
    String invalidSql = "INVALID SQL STATEMENT";

    // Act
    boolean result = executionService.isSelectQuery(invalidSql);

    // Assert
    assertFalse(result);
  }

  @Test
  void readStreamWithLimit_success_withinLimit() {
    // Arrange
    String testData = "Test output data";
    InputStream inputStream = new ByteArrayInputStream(testData.getBytes());
    ReflectionTestUtils.setField(executionService, "maxOutputSize", 1024);

    // Act
    String result = ReflectionTestUtils.invokeMethod(executionService, "readStreamWithLimit", inputStream, "stdout");

    // Assert
    assertEquals(testData, result);
  }

  @Test
  void readStreamWithLimit_truncated_exceedsLimit() {
    // Arrange
    String testData = "This is a very long test data that exceeds the limit";
    InputStream inputStream = new ByteArrayInputStream(testData.getBytes());
    ReflectionTestUtils.setField(executionService, "maxOutputSize", 10); // Very small limit

    // Act
    String result = ReflectionTestUtils.invokeMethod(executionService, "readStreamWithLimit", inputStream, "stdout");

    // Assert
    assertTrue(result.contains("[OUTPUT TRUNCATED"));
    assertTrue(result.length() > 10); // Should include truncation message
  }


  // ========== PYTHON SCRIPT EXECUTION TESTS ==========
  // Note: These tests focus on the logic flow rather than actual Python execution
  // since we cannot easily mock the Process and file operations in unit tests

  @Test
  void runPythonScript_basicStructure() {
    // Arrange
    ExecutionScriptRequest request = createTestRequest();
    ExecutionEntity execution = createTestExecution();
    execution.setScript("print('Hello World')");

    ReflectionTestUtils.setField(executionService, "executeTimeOut", 1);

    when(executionRepository.getReferenceById(request.getExecutionId())).thenReturn(execution);

    // Act
    ExecutionScriptResponse response = ReflectionTestUtils.invokeMethod(executionService, "runPythonScript", request, new ArrayList<>());

    // Assert
    assertNotNull(response);
    // The actual status depends on whether Python is available in the test environment
    // So we just verify the response is created
  }

  // ========== HELPER METHODS ==========

  /**
   * Creates a test request for use in tests.
   */
  private ExecutionScriptRequest createTestRequest() {
    ExecutionScriptRequest request = new ExecutionScriptRequest();
    request.setExecutionId("test-execution-id");
    request.setExecutionBy("Test User");

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("TEST_PARAM");
    param.setValue("test_value");
    params.add(param);

    request.setParams(params);
    return request;
  }

  /**
   * Creates a test execution entity for use in tests.
   */
  private ExecutionEntity createTestExecution() {
    ExecutionEntity execution = new ExecutionEntity();
    execution.setId("test-execution-id");
    execution.setName("Test Execution");
    execution.setDescription("Test Description");
    execution.setType(ExecutionTypeEnum.PYTHON);
    execution.setScript("print('Hello, World!')");
    return execution;
  }

  /**
   * Creates a test database connection entity for use in tests.
   */
  private DatabaseConnectionEntity createTestDatabaseConnection() {
    DatabaseConnectionEntity dbConnection = new DatabaseConnectionEntity();
    dbConnection.setId(1L);
    dbConnection.setName("Test DB Connection");
    dbConnection.setIsActive(true);
    dbConnection.setType(DatabaseConnectionTypeEnum.ORACLE); // Set the required type
    dbConnection.setHost("localhost");
    dbConnection.setPort(1521); // Use Oracle default port
    dbConnection.setUserName("testuser");
    dbConnection.setPassword("testpass");
    dbConnection.setOracleConnectType(OracleDatabaseConnectType.SID); // Set Oracle connection type
    dbConnection.setSid("ORCL"); // Set SID for Oracle
    return dbConnection;
  }
}
