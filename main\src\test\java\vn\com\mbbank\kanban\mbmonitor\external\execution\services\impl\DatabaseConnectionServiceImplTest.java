package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.DatabaseConnectionRepository;

@ExtendWith(MockitoExtension.class)
public class DatabaseConnectionServiceImplTest {
  @Mock
  DatabaseConnectionRepository databaseConnectionRepository;

  @InjectMocks
  DatabaseConnectionServiceImpl databaseConnectionService;

  @TestForDev
  void getRepository_success() {
    assertEquals(databaseConnectionRepository, databaseConnectionService.getRepository());
  }

}

